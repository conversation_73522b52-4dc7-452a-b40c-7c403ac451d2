import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'features/auth/presentation/screens/sign_in_screen.dart';
import 'features/auth/presentation/screens/sign_up_screen.dart';
import 'features/main/presentation/screens/main_screen.dart';
import 'features/onboarding/presentation/screens/onboarding_screen.dart';
import 'features/auth/domain/providers/auth_provider.dart';
import 'features/onboarding/data/repositories/onboarding_repository.dart';
import 'features/workout/presentation/screens/workout_loading_screen.dart';
import 'features/workout/presentation/screens/workout_countdown_screen.dart';
import 'features/workout/presentation/screens/pre_workout_screen.dart';
import 'features/workout/presentation/screens/active_workout_screen.dart';
import 'features/workout/presentation/screens/rest_screen.dart';
import 'features/workout/domain/models/workout_session.dart';
import 'features/dashboard/domain/models/today_workout.dart';
import 'shared/services/supabase_service.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/theme_data.dart';
import 'core/theme/providers/theme_provider.dart';
import 'core/accessibility/accessibility_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SupabaseService.initialize();

  // Initialize SharedPreferences for theme persistence
  final sharedPreferences = await SharedPreferences.getInstance();

  // Initialize accessibility service
  await AccessibilityService.instance.initialize();

  // Suppress overflow warnings in debug mode for better development experience
  if (kDebugMode) {
    FlutterError.onError = (FlutterErrorDetails details) {
      if (details.exception.toString().contains('RenderFlex overflowed')) {
        // Suppress overflow errors in debug mode
        return;
      }
      FlutterError.presentError(details);
    };
  }

  runApp(
    ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(sharedPreferences),
      ],
      child: const MyApp(),
    ),
  );
}

final goRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);

  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) async {
      // TEMPORARY: Skip authentication for testing workout UI
      // Comment out the authentication checks to bypass login

      /*
      final isAuthenticated = authState.valueOrNull?.session != null;
      final isAuthRoute = state.matchedLocation == '/signin' || state.matchedLocation == '/signup';
      final isOnboardingRoute = state.matchedLocation == '/onboarding';

      if (!isAuthenticated && !isAuthRoute) {
        return '/signin';
      }

      if (isAuthenticated && isAuthRoute) {
        // Check if user has completed onboarding
        final user = authState.valueOrNull?.session?.user;
        if (user != null) {
          final repository = OnboardingRepository();
          final hasCompleted = await repository.hasCompletedOnboarding(user.id);
          return hasCompleted ? '/' : '/onboarding';
        }
        return '/onboarding';
      }

      if (isAuthenticated && !isOnboardingRoute) {
        // Check if user needs onboarding
        final user = authState.valueOrNull?.session?.user;
        if (user != null) {
          final repository = OnboardingRepository();
          final hasCompleted = await repository.hasCompletedOnboarding(user.id);
          if (!hasCompleted) {
            return '/onboarding';
          }
        }
      }
      */

      return null;
    },
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const MainScreen(),
      ),
      GoRoute(
        path: '/signin',
        builder: (context, state) => const SignInScreen(),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      // Workout flow routes
      GoRoute(
        path: '/workout-loading',
        builder: (context, state) {
          final workoutId = state.extra as String?;
          return WorkoutLoadingScreen(workoutId: workoutId);
        },
      ),
      GoRoute(
        path: '/workout-countdown',
        builder: (context, state) {
          final workout = state.extra as TodayWorkout;
          return WorkoutCountdownScreen(workout: workout);
        },
      ),
      GoRoute(
        path: '/pre-workout',
        builder: (context, state) {
          final workout = state.extra as WorkoutSession;
          return PreWorkoutScreen(workout: workout);
        },
      ),
      GoRoute(
        path: '/active-workout',
        builder: (context, state) {
          final workout = state.extra as WorkoutSession;
          return ActiveWorkoutScreen(workout: workout);
        },
      ),
      GoRoute(
        path: '/rest-screen',
        builder: (context, state) {
          final args = state.extra as Map<String, dynamic>;
          return RestScreen(
            restDuration: args['duration'] as Duration,
            isLastSet: args['isLastSet'] as bool? ?? false,
            nextExercise: args['nextExercise'] as WorkoutExercise?,
            onRestComplete: args['onRestComplete'] as VoidCallback?,
            onSkip: args['onSkip'] as VoidCallback?,
          );
        },
      ),
    ],
  );
});

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(goRouterProvider);
    final themeData = ref.watch(themeProvider);

    return MaterialApp.router(
      title: 'Fitness App',
      debugShowCheckedModeBanner: false,
      routerConfig: router,
      themeMode: themeData.mode.themeMode,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
    );
  }
}
