import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';
import '../../domain/providers/workout_session_provider.dart';

/// Loading screen shown during workout preparation
class WorkoutLoadingScreen extends ConsumerStatefulWidget {
  final String? workoutId;

  const WorkoutLoadingScreen({
    super.key,
    this.workoutId,
  });

  @override
  ConsumerState<WorkoutLoadingScreen> createState() => _WorkoutLoadingScreenState();
}

class _WorkoutLoadingScreenState extends ConsumerState<WorkoutLoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  final List<LoadingStep> _loadingSteps = [
    LoadingStep('Loading exercises', Duration(milliseconds: 400)),
    LoadingStep('Downloading videos', Duration(milliseconds: 1100)),
    LoadingStep('Setting up timer', Duration(milliseconds: 500)),
  ];

  int _currentStepIndex = 0;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startLoadingProcess();
  }

  void _setupAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _startLoadingProcess() async {
    try {
      // Get the specific workout or today's workout
      final allWorkouts = await ref.read(allWorkoutsProvider.future);

      // Find the specific workout if ID is provided
      final targetWorkout = widget.workoutId != null
          ? allWorkouts.firstWhere(
              (w) => w.id == widget.workoutId,
              orElse: () => allWorkouts.isNotEmpty ? allWorkouts.first : throw Exception('No workout found'),
            )
          : allWorkouts.isNotEmpty
              ? allWorkouts.first
              : throw Exception('No workouts available');

      // Start the workout flow
      await ref.read(workoutSessionProvider.notifier).startWorkoutFlow(targetWorkout);

      // Animate through loading steps
      _progressController.forward();

      for (int i = 0; i < _loadingSteps.length; i++) {
        setState(() {
          _currentStepIndex = i;
        });

        await Future.delayed(_loadingSteps[i].duration);
      }

      setState(() {
        _isComplete = true;
      });

      // Navigate to countdown screen after a brief delay
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        context.go('/workout-countdown', extra: targetWorkout);
      }

    } catch (e) {
      _showError(e.toString());
    }
  }

  void _showError(String message) {
    setState(() {
      _isComplete = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.error,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _currentStepIndex = 0;
              _isComplete = false;
            });
            _progressController.reset();
            _startLoadingProcess();
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo with pulse animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: AppColorPalette.primaryGradient,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColorPalette.primaryOrange.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.fitness_center,
                        color: Colors.white,
                        size: 60,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppSpacing.xxl),

              // OpenFit title
              Text(
                'OpenFit',
                style: theme.textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.5,
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Progress bar
              GlassMorphismCard(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: Column(
                  children: [
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return LinearProgressIndicator(
                          value: _progressAnimation.value,
                          backgroundColor: Colors.white.withOpacity(0.1),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColorPalette.primaryOrange,
                          ),
                          minHeight: 8,
                        );
                      },
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      'Preparing your workout',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Loading steps
              Column(
                children: _loadingSteps.asMap().entries.map((entry) {
                  final index = entry.key;
                  final step = entry.value;
                  final isActive = index == _currentStepIndex && !_isComplete;
                  final isCompleted = index < _currentStepIndex || _isComplete;

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                    child: Row(
                      children: [
                        // Status icon
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isCompleted 
                                ? AppColorPalette.successGreen
                                : isActive 
                                    ? AppColorPalette.primaryOrange
                                    : Colors.white.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          child: isCompleted
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : isActive
                                  ? SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : null,
                        ),

                        const SizedBox(width: AppSpacing.md),

                        // Step text
                        Text(
                          step.title,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: isCompleted || isActive
                                ? Colors.white
                                : Colors.white.withOpacity(0.6),
                            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: AppSpacing.xxl),

              // Cancel button
              TextButton(
                onPressed: () {
                  context.go('/');
                },
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading step model
class LoadingStep {
  final String title;
  final Duration duration;

  const LoadingStep(this.title, this.duration);
}
