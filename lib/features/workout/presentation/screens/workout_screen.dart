import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../shared/widgets/themed_components.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';
import '../../../dashboard/domain/models/today_workout.dart';

class WorkoutScreen extends ConsumerStatefulWidget {
  const WorkoutScreen({super.key});

  @override
  ConsumerState<WorkoutScreen> createState() => _WorkoutScreenState();
}

class _WorkoutScreenState extends ConsumerState<WorkoutScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _cardController;
  late List<Animation<double>> _cardAnimations;
  
  String _selectedCategory = 'All';
  
  final List<Map<String, dynamic>> _categories = [
    {'name': 'All', 'icon': Icons.apps, 'color': AppColorPalette.primaryOrange},
    {'name': 'Strength', 'icon': Icons.fitness_center, 'color': AppColorPalette.accentBlue},
    {'name': 'Cardio', 'icon': Icons.directions_run, 'color': AppColorPalette.error},
    {'name': 'Yoga', 'icon': Icons.self_improvement, 'color': Color(0xFF8B5CF6)},
    {'name': 'HIIT', 'icon': Icons.flash_on, 'color': AppColorPalette.warning},
  ];

  // Remove hardcoded workouts - will use provider data instead

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _cardAnimations = AnimationUtils.createStaggeredAnimations(
      controller: _cardController,
      count: 6,
      staggerDelay: const Duration(milliseconds: 100),
    );
    
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _cardController.dispose();
    super.dispose();
  }

  List<TodayWorkout> _getFilteredWorkouts(List<TodayWorkout> workouts) {
    if (_selectedCategory == 'All') {
      return workouts;
    }
    return workouts.where((workout) => _getWorkoutCategory(workout) == _selectedCategory).toList();
  }

  String _getWorkoutCategory(TodayWorkout workout) {
    // Categorize workouts based on their name and content
    final name = workout.name.toLowerCase();
    if (name.contains('cardio') || name.contains('hiit') || name.contains('blast')) {
      return 'Cardio';
    } else if (name.contains('yoga') || name.contains('flow')) {
      return 'Yoga';
    } else if (name.contains('strength') || name.contains('power') || name.contains('body')) {
      return 'Strength';
    } else if (name.contains('hiit') || name.contains('circuit')) {
      return 'HIIT';
    }
    return 'Strength'; // Default category
  }

  Color _getWorkoutColor(TodayWorkout workout) {
    final category = _getWorkoutCategory(workout);
    switch (category) {
      case 'Cardio':
        return AppColorPalette.error;
      case 'Yoga':
        return const Color(0xFF8B5CF6);
      case 'HIIT':
        return AppColorPalette.warning;
      case 'Strength':
      default:
        return AppColorPalette.accentBlue;
    }
  }

  IconData _getWorkoutIcon(TodayWorkout workout) {
    final category = _getWorkoutCategory(workout);
    switch (category) {
      case 'Cardio':
        return Icons.directions_run;
      case 'Yoga':
        return Icons.self_improvement;
      case 'HIIT':
        return Icons.flash_on;
      case 'Strength':
      default:
        return Icons.fitness_center;
    }
  }

  String _getDifficultyLevel(TodayWorkout workout) {
    // Determine difficulty based on duration and exercises
    if (workout.estimatedDuration >= 50 || workout.exercises.length >= 15) {
      return 'Advanced';
    } else if (workout.estimatedDuration >= 35 || workout.exercises.length >= 10) {
      return 'Intermediate';
    }
    return 'Beginner';
  }

  List<String> _getEquipment(TodayWorkout workout) {
    // Extract equipment from exercise weights
    final hasWeights = workout.exercises.any((ex) => ex.weights.any((w) => w > 0));
    final name = workout.name.toLowerCase();

    if (name.contains('yoga')) {
      return ['Yoga Mat'];
    } else if (hasWeights) {
      if (name.contains('dumbbell')) {
        return ['Dumbbells'];
      } else if (name.contains('barbell')) {
        return ['Barbell'];
      } else {
        return ['Dumbbells', 'Barbell'];
      }
    }
    return ['None'];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final workoutsAsync = ref.watch(allWorkoutsProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: workoutsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
              const SizedBox(height: 16),
              Text('Error loading workouts: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(allWorkoutsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (workouts) => _buildWorkoutList(context, theme, textTheme, workouts),
      ),
    );
  }

  Widget _buildWorkoutList(BuildContext context, ThemeData theme, TextTheme textTheme, List<TodayWorkout> workouts) {
    final filteredWorkouts = _getFilteredWorkouts(workouts);

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
          // Custom App Bar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: FlexibleSpaceBar(
              background: AnimatedBuilder(
                animation: _fadeController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _fadeController.value,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.primaryOrange,
                            AppColorPalette.primaryOrange.withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Stack(
                        children: [
                          // Background pattern
                          Positioned(
                            right: -50,
                            top: -50,
                            child: Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white.withOpacity(0.1),
                              ),
                            ),
                          ),
                          // Content
                          SafeArea(
                            child: Padding(
                              padding: AppSpacing.paddingLg,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text(
                                    'Workouts',
                                    style: textTheme.displaySmall?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  AppSpacing.gapVerticalSm,
                                  Text(
                                    'Choose your workout for today',
                                    style: textTheme.bodyLarge?.copyWith(
                                      color: Colors.white.withOpacity(0.9),
                                    ),
                                  ),
                                  AppSpacing.gapVerticalLg,
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              title: AnimatedBuilder(
                animation: _fadeController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _fadeController.value,
                    child: Text(
                      'Workouts',
                      style: textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                },
              ),
              centerTitle: false,
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () {
                  HapticFeedback.lightImpact();
                },
              ),
              IconButton(
                icon: const Icon(Icons.filter_list, color: Colors.white),
                onPressed: () {
                  HapticFeedback.lightImpact();
                },
              ),
            ],
          ),
          
          // Categories
          SliverToBoxAdapter(
            child: AnimatedBuilder(
              animation: _cardAnimations[0],
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 20 * (1 - _cardAnimations[0].value)),
                  child: Opacity(
                    opacity: _cardAnimations[0].value,
                    child: Container(
                      height: 120,
                      padding: EdgeInsets.only(top: AppSpacing.lg),
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.symmetric(horizontal: AppSpacing.lg),
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = category['name'] == _selectedCategory;
                          
                          return GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              setState(() {
                                _selectedCategory = category['name'];
                              });
                            },
                            child: Container(
                              width: 80,
                              margin: EdgeInsets.only(right: AppSpacing.md),
                              child: Column(
                                children: [
                                  AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    width: 60,
                                    height: 60,
                                    decoration: BoxDecoration(
                                      gradient: isSelected
                                          ? LinearGradient(
                                              colors: [
                                                category['color'],
                                                category['color'].withOpacity(0.7),
                                              ],
                                            )
                                          : null,
                                      color: isSelected
                                          ? null
                                          : theme.colorScheme.surface,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: isSelected
                                          ? [
                                              BoxShadow(
                                                color: category['color'].withOpacity(0.3),
                                                blurRadius: 10,
                                                offset: const Offset(0, 5),
                                              ),
                                            ]
                                          : null,
                                    ),
                                    child: Icon(
                                      category['icon'],
                                      color: isSelected
                                          ? Colors.white
                                          : theme.colorScheme.onSurface,
                                      size: 28,
                                    ),
                                  ),
                                  AppSpacing.gapVerticalSm,
                                  Text(
                                    category['name'],
                                    style: textTheme.bodySmall?.copyWith(
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: isSelected
                                          ? category['color']
                                          : theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Popular Workouts Header
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.lg,
                vertical: AppSpacing.md,
              ),
              child: AnimatedBuilder(
                animation: _cardAnimations[1],
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, 20 * (1 - _cardAnimations[1].value)),
                    child: Opacity(
                      opacity: _cardAnimations[1].value,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Popular Workouts',
                            style: textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${filteredWorkouts.length} workouts',
                            style: textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Workout List
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: AppSpacing.lg),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final workout = filteredWorkouts[index];
                  final animationIndex = index + 2;
                  
                  return AnimatedBuilder(
                    animation: animationIndex < _cardAnimations.length
                        ? _cardAnimations[animationIndex]
                        : _cardAnimations.last,
                    builder: (context, child) {
                      final animation = animationIndex < _cardAnimations.length
                          ? _cardAnimations[animationIndex]
                          : _cardAnimations.last;
                      
                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - animation.value)),
                        child: Opacity(
                          opacity: animation.value,
                          child: Container(
                            margin: EdgeInsets.only(bottom: AppSpacing.md),
                            child: GlassMorphismCard(
                              borderRadius: 16,
                              child: InkWell(
                                onTap: () {
                                  HapticFeedback.lightImpact();
                                  // Navigate to workout details
                                },
                                borderRadius: AppSpacing.borderRadiusLg,
                                child: Column(
                                  children: [
                                    // Workout Header
                                    Container(
                                      height: 100,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            _getWorkoutColor(workout),
                                            _getWorkoutColor(workout).withOpacity(0.7),
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(AppSpacing.radiusLg),
                                          topRight: Radius.circular(AppSpacing.radiusLg),
                                        ),
                                      ),
                                      child: Stack(
                                        children: [
                                          // Background pattern
                                          Positioned(
                                            right: -30,
                                            top: -30,
                                            child: Container(
                                              width: 120,
                                              height: 120,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Colors.white.withOpacity(0.1),
                                              ),
                                            ),
                                          ),
                                          // Content
                                          ClipRect(
                                            child: Padding(
                                              padding: EdgeInsets.all(8),
                                              child: Row(
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Container(
                                                        padding: EdgeInsets.symmetric(
                                                          horizontal: 8,
                                                          vertical: 4,
                                                        ),
                                                        decoration: BoxDecoration(
                                                          color: Colors.white.withOpacity(0.2),
                                                          borderRadius: BorderRadius.circular(4),
                                                        ),
                                                        child: Text(
                                                          _getDifficultyLevel(workout),
                                                          style: textTheme.labelSmall?.copyWith(
                                                            color: Colors.white,
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(height: 6),
                                                      Text(
                                                        workout.name,
                                                        style: textTheme.titleSmall?.copyWith(
                                                          color: Colors.white,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                        maxLines: 1,
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                      SizedBox(height: 2),
                                                      Row(
                                                        children: [
                                                          Icon(
                                                            Icons.timer,
                                                            color: Colors.white.withOpacity(0.8),
                                                            size: 12,
                                                          ),
                                                          SizedBox(width: 4),
                                                          Text(
                                                            '${workout.estimatedDuration} min',
                                                            style: textTheme.bodySmall?.copyWith(
                                                              color: Colors.white.withOpacity(0.8),
                                                              fontSize: 11,
                                                            ),
                                                          ),
                                                          SizedBox(width: 12),
                                                          Icon(
                                                            Icons.fitness_center,
                                                            color: Colors.white.withOpacity(0.8),
                                                            size: 12,
                                                          ),
                                                          SizedBox(width: 4),
                                                          Text(
                                                            '${workout.exercises.length} ex',
                                                            style: textTheme.bodySmall?.copyWith(
                                                              color: Colors.white.withOpacity(0.8),
                                                              fontSize: 11,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Icon(
                                                  _getWorkoutIcon(workout),
                                                  color: Colors.white.withOpacity(0.3),
                                                  size: 32,
                                                ),
                                              ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Workout Details
                                    Padding(
                                      padding: AppSpacing.paddingLg,
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                child: Row(
                                                  children: [
                                                  Container(
                                                    padding: AppSpacing.paddingSm,
                                                    decoration: BoxDecoration(
                                                      color: AppColorPalette.error.withOpacity(0.1),
                                                      borderRadius: AppSpacing.borderRadiusSm,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons.local_fire_department,
                                                          color: AppColorPalette.error,
                                                          size: 16,
                                                        ),
                                                        AppSpacing.gapHorizontalXs,
                                                        Text(
                                                          '${workout.estimatedCalories} cal',
                                                          style: textTheme.bodySmall?.copyWith(
                                                            color: AppColorPalette.error,
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  AppSpacing.gapHorizontalMd,
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.star,
                                                        color: AppColorPalette.warning,
                                                        size: 16,
                                                      ),
                                                      AppSpacing.gapHorizontalXs,
                                                      Text(
                                                        '4.8', // Static rating for now
                                                        style: textTheme.bodySmall?.copyWith(
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                                ),
                                              ),
                                              Flexible(
                                                child: ThemedButton(
                                                text: 'Start',
                                                onPressed: () => _startWorkout(context, workout),
                                                type: ButtonType.primary,
                                                ),
                                              ),
                                            ],
                                          ),
                                          if (_getEquipment(workout).isNotEmpty &&
                                              !_getEquipment(workout).contains('None')) ...[
                                            AppSpacing.gapVerticalMd,
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.sports_martial_arts,
                                                  size: 16,
                                                  color: theme.colorScheme.onSurfaceVariant,
                                                ),
                                                AppSpacing.gapHorizontalSm,
                                                Text(
                                                  'Equipment: ',
                                                  style: textTheme.bodySmall?.copyWith(
                                                    color: theme.colorScheme.onSurfaceVariant,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    _getEquipment(workout).join(', '),
                                                    style: textTheme.bodySmall?.copyWith(
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
                childCount: filteredWorkouts.length,
              ),
            ),
          ),
          
          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _fadeController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fadeController.value,
            child: FloatingActionButton.extended(
              onPressed: () {
                HapticFeedback.mediumImpact();
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Workout'),
              backgroundColor: AppColorPalette.primaryOrange,
            ),
          );
        },
      ),
    );
  }

  /// Start a workout by navigating to the workout loading screen
  void _startWorkout(BuildContext context, TodayWorkout workout) {
    HapticFeedback.mediumImpact();

    // Navigate to workout loading screen with the selected workout
    context.go('/workout-loading', extra: workout.id);
  }
}