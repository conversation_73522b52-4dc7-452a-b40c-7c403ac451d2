import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dashboard_data.dart';
import '../models/user_stats.dart';
import '../models/today_workout.dart';
import '../../data/repositories/dashboard_repository.dart';

/// Provider for dashboard data
final dashboardRepositoryProvider = Provider<DashboardRepository>((ref) {
  return DashboardRepository();
});

/// Provider for user stats
final userStatsProvider = FutureProvider<UserStats>((ref) async {
  final repository = ref.read(dashboardRepositoryProvider);
  return repository.getUserStats();
});

/// Provider for today's workout
final todayWorkoutProvider = FutureProvider<TodayWorkout?>((ref) async {
  final repository = ref.read(dashboardRepositoryProvider);
  return repository.getTodayWorkout();
});

/// Provider for dashboard data
final dashboardDataProvider = FutureProvider<DashboardData>((ref) async {
  final userStats = await ref.watch(userStatsProvider.future);
  final todayWorkout = await ref.watch(todayWorkoutProvider.future);
  
  return DashboardData(
    userStats: userStats,
    todayWorkout: todayWorkout,
  );
});

/// Provider for greeting message based on time of day
final greetingMessageProvider = Provider<String>((ref) {
  final hour = DateTime.now().hour;
  
  if (hour < 12) {
    return 'Good morning';
  } else if (hour < 17) {
    return 'Good afternoon';
  } else {
    return 'Good evening';
  }
});

/// Provider for motivational subtitle
final motivationalSubtitleProvider = Provider<String>((ref) {
  final hour = DateTime.now().hour;
  final dayOfWeek = DateTime.now().weekday;

  if (hour < 6) {
    return 'Early bird gets the gains!';
  } else if (hour < 12) {
    return 'Ready to crush your goals today?';
  } else if (hour < 17) {
    return 'Keep the momentum going!';
  } else if (dayOfWeek == DateTime.friday) {
    return 'Finish the week strong!';
  } else {
    return 'Time to push your limits!';
  }
});

/// Provider for all available workouts
final allWorkoutsProvider = FutureProvider<List<TodayWorkout>>((ref) async {
  final repository = ref.read(dashboardRepositoryProvider);
  return repository.getAllWorkouts();
});
