import 'package:flutter/foundation.dart';
import '../../../../shared/services/supabase_service.dart';
import '../../domain/models/user_stats.dart';
import '../../domain/models/today_workout.dart';

class DashboardRepository {
  /// Get user statistics from Supabase
  Future<UserStats> getUserStats() async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get completed workouts for streak calculation
      final completedWorkouts = await SupabaseService.client
          .from('completed_workouts')
          .select('date_completed, duration, calories_burned')
          .eq('user_id', userId)
          .order('date_completed', ascending: false);

      // Calculate current streak
      final currentStreak = _calculateStreak(completedWorkouts);

      // Get weekly activity (last 7 days)
      final weekStart = DateTime.now().subtract(const Duration(days: 7));
      final weeklyWorkouts = completedWorkouts
          .where((workout) {
            final date = DateTime.parse(workout['date_completed']);
            return date.isAfter(weekStart);
          })
          .toList();

      // Calculate weekly stats
      final weeklyCalories = weeklyWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
      );

      final totalDuration = weeklyWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['duration'] as int? ?? 0),
      );

      final averageDuration = weeklyWorkouts.isNotEmpty 
          ? totalDuration / weeklyWorkouts.length 
          : 0.0;

      // Generate weekly activity data
      final weeklyActivity = _generateWeeklyActivity(weeklyWorkouts);

      // Get last workout date
      final lastWorkoutDate = completedWorkouts.isNotEmpty
          ? DateTime.parse(completedWorkouts.first['date_completed'])
          : DateTime.now().subtract(const Duration(days: 365));

      // Check if user has workout today
      final today = DateTime.now();
      final hasWorkoutToday = completedWorkouts.any((workout) {
        final date = DateTime.parse(workout['date_completed']);
        return date.year == today.year &&
               date.month == today.month &&
               date.day == today.day;
      });

      return UserStats(
        currentStreak: currentStreak,
        weeklyWorkouts: weeklyWorkouts.length,
        weeklyCalories: weeklyCalories,
        totalWorkouts: completedWorkouts.length,
        averageWorkoutDuration: averageDuration,
        weeklyActivity: weeklyActivity,
        lastWorkoutDate: lastWorkoutDate,
        hasWorkoutToday: hasWorkoutToday,
        workoutsThisWeek: weeklyWorkouts.length,
        totalCalories: weeklyCalories,
      );
    } catch (e) {
      // Return mock user stats for development/demo
      return _createMockUserStats();
    }
  }

  /// Get today's assigned workout
  Future<TodayWorkout?> getTodayWorkout() async {
    try {
      // For now, always return mock data to get the button working
      // TODO: Fix the database query later
      return _createMockWorkout();

      /* Commented out until we fix the database query issue
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get user's most recent active workout
      final workoutResponse = await SupabaseService.client
          .from('workouts')
          .select('''
            id,
            name,
            ai_description,
            is_completed,
            start_time,
            end_time,
            workout_exercises (
              id,
              name,
              sets,
              reps,
              weight,
              rest_interval,
              completed,
              exercises (
                id,
                name,
                description,
                video_url,
                vertical_video,
                instructions
              )
            )
          ''')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .limit(1);

      final workoutData = workoutResponse.isNotEmpty ? workoutResponse.first : null;

      if (workoutData == null) {
        // Return mock workout data for development/demo
        return _createMockWorkout();
      }
      */

      /* Commented out database code
      // Convert to TodayWorkout model
      final exercises = (workoutData['workout_exercises'] as List)
          .map((exerciseData) => _mapToWorkoutExercise(exerciseData))
          .toList();

      final totalSets = exercises.fold<int>(
        0,
        (sum, exercise) => sum + exercise.sets,
      );

      // Estimate duration (2 minutes per set + rest intervals)
      final estimatedDuration = exercises.fold<int>(
        0,
        (sum, exercise) => sum + (exercise.sets * 2) +
                          ((exercise.sets - 1) * (exercise.restInterval ~/ 60)),
      );

      // Estimate calories (rough calculation based on duration)
      final estimatedCalories = (estimatedDuration * 8).round();

      return TodayWorkout(
        id: workoutData['id'],
        name: workoutData['name'],
        description: workoutData['ai_description'] ?? 'Your personalized workout',
        estimatedDuration: estimatedDuration,
        estimatedCalories: estimatedCalories,
        totalSets: totalSets,
        exercises: exercises,
        isCompleted: workoutData['is_completed'] ?? false,
        isStarted: workoutData['start_time'] != null,
        startedAt: workoutData['start_time'] != null
            ? DateTime.parse(workoutData['start_time'])
            : null,
        completedAt: workoutData['end_time'] != null
            ? DateTime.parse(workoutData['end_time'])
            : null,
      );
      */
    } catch (e) {
      // Return mock workout data for development/demo
      return _createMockWorkout();
    }
  }

  /// Calculate current workout streak
  int _calculateStreak(List<dynamic> completedWorkouts) {
    if (completedWorkouts.isEmpty) return 0;

    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    // Check if user worked out today
    final today = completedWorkouts.any((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.year == currentDate.year &&
             date.month == currentDate.month &&
             date.day == currentDate.day;
    });

    if (!today) {
      // If no workout today, start checking from yesterday
      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    // Count consecutive days with workouts
    for (int i = 0; i < completedWorkouts.length; i++) {
      final workoutDate = DateTime.parse(completedWorkouts[i]['date_completed']);
      
      if (workoutDate.year == currentDate.year &&
          workoutDate.month == currentDate.month &&
          workoutDate.day == currentDate.day) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else if (workoutDate.isBefore(currentDate)) {
        break;
      }
    }

    return streak;
  }

  /// Generate weekly activity data
  List<WeeklyActivity> _generateWeeklyActivity(List<dynamic> weeklyWorkouts) {
    final List<WeeklyActivity> activity = [];
    final now = DateTime.now();

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayWorkouts = weeklyWorkouts.where((workout) {
        final workoutDate = DateTime.parse(workout['date_completed']);
        return workoutDate.year == date.year &&
               workoutDate.month == date.month &&
               workoutDate.day == date.day;
      }).toList();

      final totalMinutes = dayWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['duration'] as int? ?? 0),
      );

      final totalCalories = dayWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
      );

      activity.add(WeeklyActivity(
        day: _getDayAbbreviation(date.weekday),
        minutes: totalMinutes,
        calories: totalCalories,
        date: date,
      ));
    }

    return activity;
  }

  /// Convert workout exercise data to TodayWorkoutExercise model
  TodayWorkoutExercise _mapToWorkoutExercise(Map<String, dynamic> data) {
    final exerciseData = data['exercises'];
    final reps = (data['reps'] as List?)?.cast<int>() ?? [10];
    final weights = (data['weight'] as List?)?.cast<double>() ?? [0.0];

    return TodayWorkoutExercise(
      id: data['id'],
      name: data['name'],
      description: exerciseData?['description'] ?? '',
      sets: data['sets'] ?? 3,
      reps: reps,
      weights: weights,
      videoUrl: exerciseData?['video_url'],
      thumbnailUrl: exerciseData?['vertical_video'],
      instructions: exerciseData?['instructions'],
      restInterval: data['rest_interval'] ?? 60,
      isCompleted: data['completed'] ?? false,
    );
  }

  /// Get day abbreviation for weekday
  String _getDayAbbreviation(int weekday) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }

  /// Create mock workout data for development/demo
  TodayWorkout _createMockWorkout() {
    final mockExercises = [
      TodayWorkoutExercise(
        id: 'mock_1',
        name: 'Bent Over Row',
        description: 'Pull the barbell towards your lower chest while keeping your back straight.',
        sets: 3,
        reps: [12, 10, 8],
        weights: [45.0, 50.0, 55.0],
        restInterval: 90,
        instructions: 'Keep your core tight and pull with your back muscles, not your arms.',
      ),
      TodayWorkoutExercise(
        id: 'mock_2',
        name: 'Push-ups',
        description: 'Classic bodyweight exercise for chest, shoulders, and triceps.',
        sets: 3,
        reps: [15, 12, 10],
        weights: [0.0, 0.0, 0.0], // Bodyweight
        restInterval: 60,
        instructions: 'Keep your body in a straight line from head to heels.',
      ),
      TodayWorkoutExercise(
        id: 'mock_3',
        name: 'Squats',
        description: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings.',
        sets: 4,
        reps: [15, 12, 10, 8],
        weights: [95.0, 115.0, 135.0, 155.0],
        restInterval: 120,
        instructions: 'Keep your chest up and knees tracking over your toes.',
      ),
      TodayWorkoutExercise(
        id: 'mock_4',
        name: 'Plank',
        description: 'Core strengthening exercise that builds stability.',
        sets: 3,
        reps: [60, 45, 30], // seconds
        weights: [0.0, 0.0, 0.0], // Bodyweight
        restInterval: 60,
        instructions: 'Keep your body straight and engage your core muscles.',
      ),
    ];

    return TodayWorkout(
      id: 'mock_workout_1',
      name: 'Upper Body Power',
      description: 'A balanced upper body workout focusing on strength and muscle building. Perfect for building functional strength.',
      estimatedDuration: 45,
      estimatedCalories: 320,
      totalSets: 13,
      exercises: mockExercises,
      isCompleted: false,
      isStarted: false,
    );
  }

  /// Get all available workouts for the workout list screen
  Future<List<TodayWorkout>> getAllWorkouts() async {
    try {
      // For now, return mock data to get the functionality working
      // TODO: Replace with actual database queries later
      return _createMockWorkouts();
    } catch (e) {
      debugPrint('Error fetching all workouts: $e');
      return _createMockWorkouts();
    }
  }

  /// Create multiple mock workouts for the workout list
  List<TodayWorkout> _createMockWorkouts() {
    return [
      // Full Body Strength
      TodayWorkout(
        id: 'workout_1',
        name: 'Full Body Strength',
        description: 'A comprehensive strength training session targeting all major muscle groups.',
        estimatedDuration: 45,
        estimatedCalories: 320,
        totalSets: 12,
        exercises: [
          TodayWorkoutExercise(
            id: 'ex_1_1',
            name: 'Deadlifts',
            description: 'The king of all exercises - targets your entire posterior chain.',
            sets: 4,
            reps: [8, 6, 6, 4],
            weights: [135.0, 155.0, 175.0, 185.0],
            restInterval: 180,
            instructions: 'Keep your back straight and drive through your heels.',
          ),
          TodayWorkoutExercise(
            id: 'ex_1_2',
            name: 'Bench Press',
            description: 'Classic chest exercise for upper body strength.',
            sets: 4,
            reps: [10, 8, 6, 6],
            weights: [95.0, 115.0, 135.0, 145.0],
            restInterval: 120,
            instructions: 'Lower the bar to your chest and press up explosively.',
          ),
          TodayWorkoutExercise(
            id: 'ex_1_3',
            name: 'Pull-ups',
            description: 'Bodyweight exercise for back and bicep development.',
            sets: 4,
            reps: [8, 6, 5, 4],
            weights: [0.0, 0.0, 0.0, 0.0],
            restInterval: 90,
            instructions: 'Pull your chest to the bar with control.',
          ),
        ],
        isCompleted: false,
        isStarted: false,
      ),

      // Morning Cardio Blast
      TodayWorkout(
        id: 'workout_2',
        name: 'Morning Cardio Blast',
        description: 'High-intensity cardio session to kickstart your day with energy.',
        estimatedDuration: 30,
        estimatedCalories: 250,
        totalSets: 8,
        exercises: [
          TodayWorkoutExercise(
            id: 'ex_2_1',
            name: 'Burpees',
            description: 'Full-body explosive movement for maximum calorie burn.',
            sets: 4,
            reps: [15, 12, 10, 8],
            weights: [0.0, 0.0, 0.0, 0.0],
            restInterval: 45,
            instructions: 'Jump down, push-up, jump back up with arms overhead.',
          ),
          TodayWorkoutExercise(
            id: 'ex_2_2',
            name: 'Mountain Climbers',
            description: 'Core and cardio exercise that builds endurance.',
            sets: 4,
            reps: [30, 25, 20, 15],
            weights: [0.0, 0.0, 0.0, 0.0],
            restInterval: 30,
            instructions: 'Keep your core tight and alternate legs quickly.',
          ),
        ],
        isCompleted: false,
        isStarted: false,
      ),

      // Lower Body Power
      TodayWorkout(
        id: 'workout_3',
        name: 'Lower Body Power',
        description: 'Explosive leg workout focusing on strength and power development.',
        estimatedDuration: 50,
        estimatedCalories: 380,
        totalSets: 15,
        exercises: [
          TodayWorkoutExercise(
            id: 'ex_3_1',
            name: 'Back Squats',
            description: 'The foundation of leg strength training.',
            sets: 5,
            reps: [12, 10, 8, 6, 4],
            weights: [95.0, 115.0, 135.0, 155.0, 175.0],
            restInterval: 180,
            instructions: 'Squat down until thighs are parallel to the floor.',
          ),
          TodayWorkoutExercise(
            id: 'ex_3_2',
            name: 'Romanian Deadlifts',
            description: 'Targets hamstrings and glutes with hip hinge movement.',
            sets: 4,
            reps: [12, 10, 8, 8],
            weights: [65.0, 75.0, 85.0, 95.0],
            restInterval: 120,
            instructions: 'Keep the bar close to your legs and feel the stretch in your hamstrings.',
          ),
          TodayWorkoutExercise(
            id: 'ex_3_3',
            name: 'Bulgarian Split Squats',
            description: 'Unilateral leg exercise for balance and strength.',
            sets: 3,
            reps: [12, 10, 8],
            weights: [25.0, 30.0, 35.0],
            restInterval: 90,
            instructions: 'Keep most weight on your front leg and descend slowly.',
          ),
          TodayWorkoutExercise(
            id: 'ex_3_4',
            name: 'Calf Raises',
            description: 'Isolation exercise for calf muscle development.',
            sets: 3,
            reps: [20, 15, 12],
            weights: [45.0, 50.0, 55.0],
            restInterval: 60,
            instructions: 'Rise up on your toes and squeeze at the top.',
          ),
        ],
        isCompleted: false,
        isStarted: false,
      ),

      // Upper Body Pump
      TodayWorkout(
        id: 'workout_4',
        name: 'Upper Body Pump',
        description: 'High-volume upper body workout for muscle growth and definition.',
        estimatedDuration: 40,
        estimatedCalories: 280,
        totalSets: 14,
        exercises: [
          TodayWorkoutExercise(
            id: 'ex_4_1',
            name: 'Dumbbell Press',
            description: 'Chest exercise with greater range of motion than barbell.',
            sets: 4,
            reps: [12, 10, 8, 8],
            weights: [40.0, 45.0, 50.0, 55.0],
            restInterval: 90,
            instructions: 'Lower dumbbells to chest level and press up smoothly.',
          ),
          TodayWorkoutExercise(
            id: 'ex_4_2',
            name: 'Bent Over Rows',
            description: 'Back exercise for thickness and strength.',
            sets: 4,
            reps: [12, 10, 8, 8],
            weights: [65.0, 75.0, 85.0, 95.0],
            restInterval: 90,
            instructions: 'Pull the bar to your lower chest, squeeze shoulder blades.',
          ),
          TodayWorkoutExercise(
            id: 'ex_4_3',
            name: 'Overhead Press',
            description: 'Shoulder exercise for strength and stability.',
            sets: 3,
            reps: [10, 8, 6],
            weights: [65.0, 75.0, 85.0],
            restInterval: 120,
            instructions: 'Press the bar overhead while keeping core tight.',
          ),
          TodayWorkoutExercise(
            id: 'ex_4_4',
            name: 'Bicep Curls',
            description: 'Isolation exercise for bicep development.',
            sets: 3,
            reps: [15, 12, 10],
            weights: [25.0, 30.0, 35.0],
            restInterval: 60,
            instructions: 'Curl the weight up with control, squeeze at the top.',
          ),
        ],
        isCompleted: false,
        isStarted: false,
      ),

      // Core & Conditioning
      TodayWorkout(
        id: 'workout_5',
        name: 'Core & Conditioning',
        description: 'Intense core workout combined with conditioning for total body fitness.',
        estimatedDuration: 35,
        estimatedCalories: 220,
        totalSets: 10,
        exercises: [
          TodayWorkoutExercise(
            id: 'ex_5_1',
            name: 'Plank',
            description: 'Isometric core exercise for stability and strength.',
            sets: 4,
            reps: [60, 45, 30, 30], // seconds
            weights: [0.0, 0.0, 0.0, 0.0],
            restInterval: 45,
            instructions: 'Keep your body in a straight line from head to heels.',
          ),
          TodayWorkoutExercise(
            id: 'ex_5_2',
            name: 'Russian Twists',
            description: 'Rotational core exercise for oblique strength.',
            sets: 3,
            reps: [30, 25, 20],
            weights: [15.0, 20.0, 25.0],
            restInterval: 60,
            instructions: 'Rotate your torso side to side while holding the weight.',
          ),
          TodayWorkoutExercise(
            id: 'ex_5_3',
            name: 'Dead Bug',
            description: 'Core stability exercise that teaches proper movement patterns.',
            sets: 3,
            reps: [20, 15, 12],
            weights: [0.0, 0.0, 0.0],
            restInterval: 45,
            instructions: 'Extend opposite arm and leg while keeping core stable.',
          ),
        ],
        isCompleted: false,
        isStarted: false,
      ),

      // Yoga Flow
      TodayWorkout(
        id: 'workout_6',
        name: 'Yoga Flow',
        description: 'Relaxing yoga session for flexibility, balance, and mindfulness.',
        estimatedDuration: 25,
        estimatedCalories: 120,
        totalSets: 6,
        exercises: [
          TodayWorkoutExercise(
            id: 'ex_6_1',
            name: 'Sun Salutation',
            description: 'Classic yoga flow sequence for full body movement.',
            sets: 3,
            reps: [5, 4, 3],
            weights: [0.0, 0.0, 0.0],
            restInterval: 30,
            instructions: 'Flow through the sequence with controlled breathing.',
          ),
          TodayWorkoutExercise(
            id: 'ex_6_2',
            name: 'Warrior Pose',
            description: 'Standing pose for strength and balance.',
            sets: 3,
            reps: [45, 30, 30], // seconds each side
            weights: [0.0, 0.0, 0.0],
            restInterval: 15,
            instructions: 'Hold the pose with strong legs and open chest.',
          ),
        ],
        isCompleted: false,
        isStarted: false,
      ),
    ];
  }

  /// Create mock user stats for development/demo
  UserStats _createMockUserStats() {
    final now = DateTime.now();
    final mockWeeklyActivity = List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      return WeeklyActivity(
        day: _getDayAbbreviation(date.weekday),
        minutes: index % 2 == 0 ? 45 : 0, // Alternate workout days
        calories: index % 2 == 0 ? 320 : 0,
        date: date,
      );
    });

    return UserStats(
      currentStreak: 3,
      weeklyWorkouts: 3,
      weeklyCalories: 960,
      totalWorkouts: 24,
      averageWorkoutDuration: 42.5,
      weeklyActivity: mockWeeklyActivity,
      lastWorkoutDate: now.subtract(const Duration(days: 1)),
      hasWorkoutToday: false,
      workoutsThisWeek: 3,
      totalCalories: 960,
    );
  }
}
